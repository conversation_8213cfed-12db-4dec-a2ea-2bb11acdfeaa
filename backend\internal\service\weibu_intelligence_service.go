package service

import (
	"fmt"
	"time"

	"gorm.io/gorm"
	//"vulnerability_push/IOC_Feed"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

// WeibuIntelligenceService 微步威胁情报服务
type WeibuIntelligenceService struct {
	db *gorm.DB
}

// WeibuQueryRequest 微步威胁情报查询请求
type WeibuQueryRequest struct {
	IOC   string `json:"ioc" binding:"required"`   // IOC值（目前仅支持IP）
	IOCId uint   `json:"iocId,omitempty"`          // IOC情报ID（可选，用于更新数据库记录）
}

// WeibuQueryResponse 微步威胁情报查询响应
type WeibuQueryResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// NewWeibuIntelligenceService 创建微步威胁情报服务
func NewWeibuIntelligenceService(db *gorm.DB) *WeibuIntelligenceService {
	return &WeibuIntelligenceService{
		db: db,
	}
}

// QueryIOC 查询单个IOC的威胁情报（目前仅支持IP）
func (s *WeibuIntelligenceService) QueryIOC(req WeibuQueryRequest) (*WeibuQueryResponse, error) {
	// 调用微步威胁情报查询服务
	weibuResult := IOC_Feed.QueryWeibuIPReputationGlobal(req.IOC)
	if weibuResult == nil {
		return &WeibuQueryResponse{
			Success: false,
			Message: "微步威胁情报服务不可用",
		}, fmt.Errorf("微步威胁情报服务不可用")
	}

	if !weibuResult.Success {
		response := &WeibuQueryResponse{
			Success: false,
			Message: "查询微步威胁情报失败: " + weibuResult.ErrorMessage,
		}

		// 如果提供了IOC ID，更新数据库记录为错误状态
		if req.IOCId > 0 {
			if updateErr := s.updateIOCRecord(req.IOCId, nil, weibuResult.ErrorMessage); updateErr != nil {
				utils.Errorf("更新IOC情报微步数据失败: %v", updateErr)
			}
		}

		return response, fmt.Errorf(weibuResult.ErrorMessage)
	}

	// 如果提供了IOC ID，更新数据库记录
	if req.IOCId > 0 {
		if updateErr := s.updateIOCRecord(req.IOCId, weibuResult, ""); updateErr != nil {
			// 记录错误但不影响返回结果
			fmt.Printf("更新IOC情报微步数据失败: %v\n", updateErr)
		}
	}

	return &WeibuQueryResponse{
		Success: true,
		Message: "查询成功",
		Data:    weibuResult.Data,
	}, nil
}

// updateIOCRecord 更新IOC记录的微步数据
func (s *WeibuIntelligenceService) updateIOCRecord(iocId uint, weibuResult *IOC_Feed.WeibuIntelligenceResult, errorMessage string) error {
	updateData := map[string]interface{}{
		"weibu_query_time": time.Now().Unix(),
	}

	if errorMessage != "" {
		updateData["weibu_query_status"] = "error"
		updateData["weibu_error_message"] = errorMessage
	} else {
		updateData["weibu_query_status"] = "success"
		updateData["weibu_error_message"] = ""

		if serializedData, err := weibuResult.SerializeData(); err == nil {
			updateData["weibu_data"] = serializedData
		} else {
			updateData["weibu_error_message"] = fmt.Sprintf("序列化微步数据失败: %v", err)
		}
	}

	return s.db.Model(&models.IOCIntelligence{}).Where("id = ?", iocId).Updates(updateData).Error
}

// BatchQueryIPs 批量查询IP威胁情报
func (s *WeibuIntelligenceService) BatchQueryIPs(ips []string) (map[string]*IOC_Feed.WeibuIntelligenceResult, error) {
	if len(ips) == 0 {
		return nil, fmt.Errorf("IP列表为空")
	}

	results := make(map[string]*IOC_Feed.WeibuIntelligenceResult)

	for _, ip := range ips {
		weibuResult := IOC_Feed.QueryWeibuIPReputationGlobal(ip)
		if weibuResult != nil {
			results[ip] = weibuResult
		} else {
			// 创建一个错误结果
			results[ip] = &IOC_Feed.WeibuIntelligenceResult{
				Success:      false,
				ErrorMessage: "微步威胁情报服务不可用",
			}
		}

		// 添加延迟避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	return results, nil
}

// BatchUpdateUnqueriedIOCs 批量更新未查询的IOC记录（仅处理IP类型）
func (s *WeibuIntelligenceService) BatchUpdateUnqueriedIOCs() (int, error) {
	// 获取所有未查询微步情报的IP类型IOC记录
	var iocList []models.IOCIntelligence
	if err := s.db.Where("(weibu_query_status = ? OR weibu_query_status IS NULL) AND ioc_type = ?", "not_queried", "ip").Find(&iocList).Error; err != nil {
		return 0, fmt.Errorf("获取IOC情报列表失败: %v", err)
	}

	if len(iocList) == 0 {
		return 0, nil
	}

	successCount := 0
	for _, ioc := range iocList {
		req := WeibuQueryRequest{
			IOC:   ioc.IOC,
			IOCId: ioc.ID,
		}

		_, err := s.QueryIOC(req)
		if err != nil {
			fmt.Printf("查询IOC %s 失败: %v\n", ioc.IOC, err)
		} else {
			successCount++
		}

		// 添加延迟避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	return successCount, nil
}

// IsIPSupported 检查是否支持该IOC类型（微步目前仅支持IP）
func (s *WeibuIntelligenceService) IsIPSupported(iocType string) bool {
	return iocType == "ip"
}

// GetSupportedTypes 获取支持的IOC类型列表
func (s *WeibuIntelligenceService) GetSupportedTypes() []string {
	return []string{"ip"}
}
