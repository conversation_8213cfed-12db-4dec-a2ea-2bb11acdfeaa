package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	//"vulnerability_push/IOC_Feed"
	"vulnerability_push/internal/models"
)

// TJUNIntelligenceService 天际友盟威胁情报服务
type TJUNIntelligenceService struct {
	db     *gorm.DB
	client *IOC_Feed.TJUNClient
	config TJUNConfig
}

// TJUNConfig 天际友盟配置
type TJUNConfig struct {
	Host      string
	AppKey    string
	AppSecret string
	Token     string
	Timeout   time.Duration
}

// TJUNQueryRequest 天际友盟查询请求
type TJUNQueryRequest struct {
	IOC     string `json:"ioc" binding:"required"`     // IOC值
	IOCType string `json:"iocType" binding:"required"` // IOC类型：ip, domain等
	IOCId   uint   `json:"iocId,omitempty"`            // IOC情报ID（可选，用于更新数据库记录）
}

// TJUNQueryResponse 天际友盟查询响应
type TJUNQueryResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// NewTJUNIntelligenceService 创建天际友盟威胁情报服务
func NewTJUNIntelligenceService(db *gorm.DB, config TJUNConfig) *TJUNIntelligenceService {
	client := IOC_Feed.NewTJUNClient(config.Host, config.AppKey, config.AppSecret)
	if config.Timeout > 0 {
		client.SetTimeout(config.Timeout)
	} else {
		client.SetTimeout(10 * time.Second) // 默认超时时间
	}

	return &TJUNIntelligenceService{
		db:     db,
		client: client,
		config: config,
	}
}

// QueryIOC 查询单个IOC的威胁情报
func (s *TJUNIntelligenceService) QueryIOC(req TJUNQueryRequest) (*TJUNQueryResponse, error) {
	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	// 根据IOC类型调用不同的接口
	var response interface{}

	if strings.ToLower(req.IOCType) == "ip" {
		// 对于IP类型，调用reputation接口
		ipArray := fmt.Sprintf(`["%s"]`, req.IOC)
		reputationResp, queryErr := s.client.RequestIOCReputation(ctx, s.config.Token, "ip", ipArray, "ip_reputation", "")
		if queryErr != nil {
			return &TJUNQueryResponse{
				Success: false,
				Message: "查询天际友盟IOC失败: " + queryErr.Error(),
			}, queryErr
		}
		response = reputationResp.ResponseData
	} else {
		// 对于其他类型，调用requestIOC接口
		iocResp, queryErr := s.client.RequestIOCV2(ctx, s.config.Token, req.IOCType, req.IOC)
		if queryErr != nil {
			return &TJUNQueryResponse{
				Success: false,
				Message: "查询天际友盟IOC失败: " + queryErr.Error(),
			}, queryErr
		}
		response = iocResp.ResponseData
	}

	// 如果提供了IOC ID，更新数据库记录
	if req.IOCId > 0 {
		if updateErr := s.updateIOCRecord(req.IOCId, response, ""); updateErr != nil {
			// 记录错误但不影响返回结果
			fmt.Printf("更新IOC情报天际友盟数据失败: %v\n", updateErr)
		}
	}

	return &TJUNQueryResponse{
		Success: true,
		Message: "查询成功",
		Data:    response,
	}, nil
}

// updateIOCRecord 更新IOC记录的天际友盟数据
func (s *TJUNIntelligenceService) updateIOCRecord(iocId uint, data interface{}, errorMessage string) error {
	updateData := map[string]interface{}{
		"tjun_query_time": time.Now().Unix(),
	}

	if errorMessage != "" {
		updateData["tjun_query_status"] = "error"
		updateData["tjun_error_message"] = errorMessage
	} else {
		updateData["tjun_query_status"] = "success"
		updateData["tjun_error_message"] = ""

		if serializedData, err := json.Marshal(data); err == nil {
			updateData["tjun_data"] = string(serializedData)
		} else {
			updateData["tjun_error_message"] = fmt.Sprintf("序列化天际友盟数据失败: %v", err)
		}
	}

	return s.db.Model(&models.IOCIntelligence{}).Where("id = ?", iocId).Updates(updateData).Error
}

// BatchQueryIOCs 批量查询IOC威胁情报
func (s *TJUNIntelligenceService) BatchQueryIOCs(iocs []string, iocType string) (map[string]interface{}, error) {
	if len(iocs) == 0 {
		return nil, fmt.Errorf("IOC列表为空")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	results := make(map[string]interface{})

	if strings.ToLower(iocType) == "ip" {
		// 对于IP类型，使用批量reputation接口
		ipArray, err := json.Marshal(iocs)
		if err != nil {
			return nil, fmt.Errorf("序列化IP列表失败: %v", err)
		}

		reputationResp, err := s.client.RequestIOCReputation(ctx, s.config.Token, "ip", string(ipArray), "ip_reputation", "")
		if err != nil {
			return nil, fmt.Errorf("批量查询天际友盟IP reputation失败: %v", err)
		}

		results["batch_result"] = reputationResp.ResponseData
	} else {
		// 对于其他类型，逐个查询
		for _, ioc := range iocs {
			iocResp, err := s.client.RequestIOCV2(ctx, s.config.Token, iocType, ioc)
			if err != nil {
				results[ioc] = map[string]interface{}{
					"error": err.Error(),
				}
			} else {
				results[ioc] = iocResp.ResponseData
			}
		}
	}

	return results, nil
}

// BatchUpdateUnqueriedIOCs 批量更新未查询的IOC记录
func (s *TJUNIntelligenceService) BatchUpdateUnqueriedIOCs() (int, error) {
	// 获取所有未查询天际友盟情报的IOC记录
	var iocList []models.IOCIntelligence
	if err := s.db.Where("tjun_query_status = ? OR tjun_query_status IS NULL", "not_queried").Find(&iocList).Error; err != nil {
		return 0, fmt.Errorf("获取IOC情报列表失败: %v", err)
	}

	if len(iocList) == 0 {
		return 0, nil
	}

	successCount := 0
	for _, ioc := range iocList {
		req := TJUNQueryRequest{
			IOC:     ioc.IOC,
			IOCType: ioc.IOCType,
			IOCId:   ioc.ID,
		}

		_, err := s.QueryIOC(req)
		if err != nil {
			// 更新错误状态
			s.updateIOCRecord(ioc.ID, nil, err.Error())
			fmt.Printf("查询IOC %s 失败: %v\n", ioc.IOC, err)
		} else {
			successCount++
		}

		// 添加延迟避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	return successCount, nil
}

// GetDefaultConfig 获取默认配置
func GetDefaultTJUNConfig() TJUNConfig {
	return TJUNConfig{
		Host:      "api.tj-un.com",
		AppKey:    "24634572",
		AppSecret: "812d078fe8143e529599f9d6689a6046",
		Token:     "9fd3b7f517dc4674b7f001e0b1ed7c61",
		Timeout:   10 * time.Second,
	}
}
